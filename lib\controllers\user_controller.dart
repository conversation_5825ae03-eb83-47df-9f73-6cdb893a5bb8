import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:dio/dio.dart' as dio_pkg;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/user.dart';
import '../services/auth_service.dart';
import '../services/user_sync_service.dart';
import '../services/payment_service.dart';
import '../config/api_config.dart';

/// 用户控制器
class UserController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final UserSyncService _syncService = Get.find<UserSyncService>();
  final PaymentService _paymentService = Get.find<PaymentService>();
  final ImagePicker _imagePicker = ImagePicker();

  // 响应式变量
  Rx<User?> get currentUser => _authService.currentUser;
  RxBool get isLoggedIn => _authService.isLoggedIn;
  RxBool get isSyncEnabled => _syncService.isSyncEnabled;
  RxBool get isSyncing => _syncService.isSyncing;
  Rx<DateTime?> get lastSyncTime => _syncService.lastSyncTime;

  final RxBool isUpdatingProfile = false.obs;
  final RxString avatarPath = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // 监听用户变化
    ever(currentUser, (User? user) {
      if (user != null) {
        avatarPath.value = user.avatar ?? '';
      }
    });
  }

  /// 更新用户名
  Future<bool> updateUsername(String newUsername) async {
    if (newUsername.trim().isEmpty) {
      Get.snackbar('错误', '用户名不能为空');
      return false;
    }

    if (newUsername.length < 2 || newUsername.length > 20) {
      Get.snackbar('错误', '用户名长度应在2-20个字符之间');
      return false;
    }

    try {
      isUpdatingProfile.value = true;
      
      final dio = dio_pkg.Dio();
      final response = await dio.put(
        ApiConfig.getEndpoint('userProfile'),
        data: {'username': newUsername},
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer ${await _getToken()}'},
        ),
      );

      if (response.data['success'] == true) {
        currentUser.value?.username = newUsername;
        currentUser.refresh();
        
        // 如果启用同步，自动同步数据
        if (isSyncEnabled.value) {
          _syncService.syncUserData();
        }
        
        Get.snackbar('成功', '用户名更新成功');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '更新失败');
        return false;
      }
    } catch (e) {
      print('更新用户名失败: $e');
      Get.snackbar('错误', '更新用户名失败: ${e.toString()}');
      return false;
    } finally {
      isUpdatingProfile.value = false;
    }
  }

  /// 更新密码
  Future<bool> updatePassword(String oldPassword, String newPassword) async {
    if (oldPassword.trim().isEmpty || newPassword.trim().isEmpty) {
      Get.snackbar('错误', '密码不能为空');
      return false;
    }

    if (newPassword.length < 6) {
      Get.snackbar('错误', '新密码长度不能少于6位');
      return false;
    }

    try {
      isUpdatingProfile.value = true;
      
      final dio = dio_pkg.Dio();
      final response = await dio.put(
        ApiConfig.getEndpoint('userPassword'),
        data: {
          'oldPassword': _hashPassword(oldPassword),
          'newPassword': _hashPassword(newPassword),
        },
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer ${await _getToken()}'},
        ),
      );

      if (response.data['success'] == true) {
        Get.snackbar('成功', '密码更新成功');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '密码更新失败');
        return false;
      }
    } catch (e) {
      print('更新密码失败: $e');
      Get.snackbar('错误', '更新密码失败: ${e.toString()}');
      return false;
    } finally {
      isUpdatingProfile.value = false;
    }
  }

  /// 选择并上传头像
  Future<void> selectAndUploadAvatar() async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        await uploadAvatar(File(image.path));
      }
    } catch (e) {
      print('选择头像失败: $e');
      Get.snackbar('错误', '选择头像失败');
    }
  }

  /// 上传头像
  Future<bool> uploadAvatar(File imageFile) async {
    try {
      isUpdatingProfile.value = true;
      
      final formData = dio_pkg.FormData.fromMap({
        'avatar': await dio_pkg.MultipartFile.fromFile(
          imageFile.path,
          filename: 'avatar.jpg',
        ),
      });

      final dio = dio_pkg.Dio();
      final response = await dio.post(
        ApiConfig.getEndpoint('userAvatar'),
        data: formData,
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer ${await _getToken()}'},
        ),
      );

      if (response.data['success'] == true) {
        final avatarUrl = response.data['data']['avatarUrl'];

        // 添加时间戳避免缓存问题
        final timestampedUrl = '$avatarUrl&t=${DateTime.now().millisecondsSinceEpoch}';

        // 更新用户头像
        if (currentUser.value != null) {
          currentUser.value!.avatar = timestampedUrl;
          currentUser.refresh();
        }
        avatarPath.value = timestampedUrl;

        print('头像更新成功: $timestampedUrl');

        // 如果启用同步，自动同步数据
        if (isSyncEnabled.value) {
          _syncService.syncUserData();
        }

        Get.snackbar('成功', '头像更新成功');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '头像上传失败');
        return false;
      }
    } catch (e) {
      print('上传头像失败: $e');
      Get.snackbar('错误', '上传头像失败: ${e.toString()}');
      return false;
    } finally {
      isUpdatingProfile.value = false;
    }
  }

  /// 更新用户设置
  Future<bool> updateSettings(UserSettings settings) async {
    try {
      isUpdatingProfile.value = true;
      
      final dio = dio_pkg.Dio();
      final response = await dio.put(
        ApiConfig.getEndpoint('userSettings'),
        data: settings.toJson(),
        options: dio_pkg.Options(
          headers: {'Authorization': 'Bearer ${await _getToken()}'},
        ),
      );

      if (response.data['success'] == true) {
        currentUser.value?.settings = settings;
        currentUser.refresh();
        
        // 如果启用同步，自动同步数据
        if (isSyncEnabled.value) {
          _syncService.syncUserData();
        }
        
        Get.snackbar('成功', '设置更新成功');
        return true;
      } else {
        Get.snackbar('错误', response.data['message'] ?? '设置更新失败');
        return false;
      }
    } catch (e) {
      print('更新设置失败: $e');
      Get.snackbar('错误', '更新设置失败: ${e.toString()}');
      return false;
    } finally {
      isUpdatingProfile.value = false;
    }
  }

  /// 切换数据同步开关
  Future<void> toggleDataSync(bool enabled) async {
    await _syncService.setSyncEnabled(enabled);
  }

  /// 手动同步数据
  Future<void> manualSync() async {
    await _syncService.syncUserData();
  }

  /// 导出用户数据
  Future<void> exportUserData() async {
    try {
      // 显示加载提示
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在导出数据...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 收集用户数据
      final data = await _syncService.exportUserData();

      // 添加导出信息
      final exportData = {
        'exportInfo': {
          'appName': '岱宗文脉',
          'exportTime': DateTime.now().toIso8601String(),
          'version': '1.0.0',
          'userId': currentUser.value?.id,
          'username': currentUser.value?.username,
        },
        'userData': data,
      };

      // 统计信息
      final stats = _generateExportStats(data);
      exportData['statistics'] = stats;

      // 生成文件名
      final timestamp = DateTime.now().toIso8601String().replaceAll(':', '-').split('.')[0];
      final username = currentUser.value?.username ?? 'user';
      final fileName = '岱宗文脉_${username}_$timestamp.json';

      // 保存到文件
      final file = await _saveDataToFile(exportData, fileName);

      // 关闭加载对话框
      Get.back();

      // 显示导出结果对话框
      _showExportResultDialog(file, stats);

    } catch (e) {
      // 关闭加载对话框
      if (Get.isDialogOpen ?? false) {
        Get.back();
      }

      print('导出数据失败: $e');
      Get.snackbar('错误', '导出数据失败: ${e.toString()}');
    }
  }

  /// 获取会员状态信息
  String getMembershipStatusText() {
    final user = currentUser.value;
    if (user == null) return '未登录';
    
    if (user.isPermanentMember) {
      return '永久会员';
    } else if (user.isValidMember) {
      final remainingDays = user.memberRemainingDays;
      return '会员 (剩余${remainingDays}天)';
    } else {
      return '免费用户';
    }
  }

  /// 获取会员权益描述
  List<String> getMembershipBenefits() {
    final user = currentUser.value;
    if (user == null || !user.isValidMember) {
      return [
        '每日最多100章节',
        '最多5个知识库文档',
        '不限制生成小说数',
        '不限制生成字数',
        '支持多种导出格式',
        '支持所有AI模型',
        '❌ 无扩展功能',
        '❌ 无数据云端同步',
      ];
    }

    return [
      '无章节数量限制',
      '最多20个知识库文档',
      '无每日生成限制',
      '无字数限制',
      '多种导出格式',
      '高级AI模型',
      '✅ 扩展功能',
      '✅ 数据云端同步',
    ];
  }

  /// 检查功能权限
  bool canUseFeature(String feature) {
    return _paymentService.canUseFeature(feature);
  }

  /// 检查使用限制
  bool isWithinLimits(String limitType, int currentCount) {
    return _paymentService.isWithinLimits(limitType, currentCount);
  }

  /// 注销账号
  Future<void> deleteAccount() async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('确认注销'),
        content: const Text('注销账号将删除所有数据，此操作不可恢复。确定要继续吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('确认注销'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final dio = dio_pkg.Dio();
        final response = await dio.delete(
          ApiConfig.getEndpoint('deleteAccount'),
          options: dio_pkg.Options(
            headers: {'Authorization': 'Bearer ${await _getToken()}'},
          ),
        );

        if (response.data['success'] == true) {
          await _authService.logout();
          Get.snackbar('成功', '账号已注销');
        } else {
          Get.snackbar('错误', response.data['message'] ?? '注销失败');
        }
      } catch (e) {
        print('注销账号失败: $e');
        Get.snackbar('错误', '注销账号失败: ${e.toString()}');
      }
    }
  }

  /// 获取当前用户Token
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  /// 密码哈希（与AuthService保持一致）
  String _hashPassword(String password) {
    final bytes = utf8.encode(password);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// 生成导出统计信息
  Map<String, dynamic> _generateExportStats(Map<String, dynamic> data) {
    return {
      'novels': (data['novels'] as List?)?.length ?? 0,
      'characterCards': (data['characterCards'] as List?)?.length ?? 0,
      'characterTypes': (data['characterTypes'] as List?)?.length ?? 0,
      'knowledgeDocuments': (data['knowledgeDocuments'] as List?)?.length ?? 0,
      'stylePackages': (data['stylePackages'] as List?)?.length ?? 0,
      'hasUserSettings': data['userSettings'] != null,
    };
  }

  /// 保存数据到文件
  Future<File> _saveDataToFile(Map<String, dynamic> data, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');

    // 格式化JSON输出
    const encoder = JsonEncoder.withIndent('  ');
    final jsonString = encoder.convert(data);

    await file.writeAsString(jsonString);
    return file;
  }

  /// 显示导出结果对话框
  void _showExportResultDialog(File file, Map<String, dynamic> stats) {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('导出成功'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('数据已成功导出到：'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                file.path,
                style: const TextStyle(fontSize: 12, fontFamily: 'monospace'),
              ),
            ),
            const SizedBox(height: 16),
            Text('导出内容统计：'),
            const SizedBox(height: 8),
            ...stats.entries.map((entry) {
              String label = '';
              switch (entry.key) {
                case 'novels':
                  label = '小说';
                  break;
                case 'characterCards':
                  label = '角色卡片';
                  break;
                case 'characterTypes':
                  label = '角色类型';
                  break;
                case 'knowledgeDocuments':
                  label = '知识库文档';
                  break;
                case 'stylePackages':
                  label = '写作风格包';
                  break;
                case 'hasUserSettings':
                  return entry.value ? const Text('✓ 用户设置') : const Text('✗ 用户设置');
                default:
                  return const SizedBox.shrink();
              }
              return Text('• $label: ${entry.value}');
            }).toList(),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
          ElevatedButton(
            onPressed: () => _shareExportFile(file),
            child: const Text('分享文件'),
          ),
        ],
      ),
    );
  }

  /// 分享导出文件
  Future<void> _shareExportFile(File file) async {
    try {
      await Share.shareXFiles(
        [XFile(file.path)],
        text: '岱宗文脉数据备份文件',
        subject: '数据导出',
      );
    } catch (e) {
      print('分享文件失败: $e');
      Get.snackbar('错误', '分享文件失败');
    }
  }
}
