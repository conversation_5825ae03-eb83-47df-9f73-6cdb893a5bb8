import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'auth_service.dart';

/// 每日限制服务
/// 管理非会员用户的每日使用限制
class DailyLimitService extends GetxController {
  static DailyLimitService get to => Get.find();
  
  final AuthService _authService = Get.find<AuthService>();
  
  // 每日章节生成计数
  final RxInt dailyChapterCount = 0.obs;
  
  // 今日日期
  final Rx<DateTime> currentDate = DateTime.now().obs;
  
  // 常量
  static const int FREE_USER_DAILY_CHAPTER_LIMIT = 100;
  static const String DAILY_CHAPTER_COUNT_KEY = 'daily_chapter_count';
  static const String LAST_COUNT_DATE_KEY = 'last_count_date';
  
  @override
  Future<void> onInit() async {
    super.onInit();
    await _loadDailyCount();
    _checkDateReset();
  }
  
  /// 加载每日计数
  Future<void> _loadDailyCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 获取上次记录的日期
      final lastDateStr = prefs.getString(LAST_COUNT_DATE_KEY);
      final today = DateTime.now();
      final todayStr = _formatDate(today);
      
      // 如果是新的一天，重置计数
      if (lastDateStr != todayStr) {
        dailyChapterCount.value = 0;
        await prefs.setInt(DAILY_CHAPTER_COUNT_KEY, 0);
        await prefs.setString(LAST_COUNT_DATE_KEY, todayStr);
        print('新的一天，重置章节计数');
      } else {
        // 加载今日计数
        dailyChapterCount.value = prefs.getInt(DAILY_CHAPTER_COUNT_KEY) ?? 0;
      }
      
      currentDate.value = today;
      print('加载每日章节计数: ${dailyChapterCount.value}');
    } catch (e) {
      print('加载每日计数失败: $e');
      dailyChapterCount.value = 0;
    }
  }
  
  /// 检查日期重置
  void _checkDateReset() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final currentDay = DateTime(currentDate.value.year, currentDate.value.month, currentDate.value.day);
    
    if (!today.isAtSameMomentAs(currentDay)) {
      _resetDailyCount();
    }
  }
  
  /// 重置每日计数
  Future<void> _resetDailyCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      dailyChapterCount.value = 0;
      currentDate.value = DateTime.now();
      
      await prefs.setInt(DAILY_CHAPTER_COUNT_KEY, 0);
      await prefs.setString(LAST_COUNT_DATE_KEY, _formatDate(currentDate.value));
      
      print('每日计数已重置');
    } catch (e) {
      print('重置每日计数失败: $e');
    }
  }
  
  /// 格式化日期为字符串
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
  
  /// 检查是否可以生成章节
  bool canGenerateChapter() {
    final user = _authService.currentUser.value;
    
    // 会员用户无限制
    if (user != null && user.isValidMember) {
      return true;
    }
    
    // 非会员用户检查每日限制
    return dailyChapterCount.value < FREE_USER_DAILY_CHAPTER_LIMIT;
  }
  
  /// 增加章节计数
  Future<bool> incrementChapterCount() async {
    final user = _authService.currentUser.value;
    
    // 会员用户无需计数
    if (user != null && user.isValidMember) {
      return true;
    }
    
    // 检查是否超出限制
    if (dailyChapterCount.value >= FREE_USER_DAILY_CHAPTER_LIMIT) {
      return false;
    }
    
    try {
      // 检查日期重置
      _checkDateReset();
      
      // 增加计数
      dailyChapterCount.value++;
      
      // 保存到本地存储
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(DAILY_CHAPTER_COUNT_KEY, dailyChapterCount.value);
      
      print('章节计数增加: ${dailyChapterCount.value}/$FREE_USER_DAILY_CHAPTER_LIMIT');
      return true;
    } catch (e) {
      print('增加章节计数失败: $e');
      return false;
    }
  }
  
  /// 获取剩余章节数
  int getRemainingChapters() {
    final user = _authService.currentUser.value;
    
    // 会员用户无限制
    if (user != null && user.isValidMember) {
      return -1; // -1 表示无限制
    }
    
    // 非会员用户返回剩余数量
    return (FREE_USER_DAILY_CHAPTER_LIMIT - dailyChapterCount.value).clamp(0, FREE_USER_DAILY_CHAPTER_LIMIT);
  }
  
  /// 获取限制信息文本
  String getLimitText() {
    final user = _authService.currentUser.value;
    
    if (user != null && user.isValidMember) {
      return '会员用户 - 无限制';
    }
    
    final remaining = getRemainingChapters();
    return '免费用户 - 今日剩余: $remaining/$FREE_USER_DAILY_CHAPTER_LIMIT 章';
  }
  
  /// 检查并显示限制提示
  bool checkAndShowLimitWarning() {
    if (canGenerateChapter()) {
      return true;
    }
    
    Get.snackbar(
      '每日限制',
      '免费用户每日最多生成100章节，您今日已达到限制。\n升级为会员可享受无限制生成。',
      duration: const Duration(seconds: 5),
    );
    return false;
  }
  
  /// 手动刷新计数（用于测试）
  Future<void> refreshCount() async {
    await _loadDailyCount();
  }
}
